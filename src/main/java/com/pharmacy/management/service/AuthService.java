package com.pharmacy.management.service;

import com.pharmacy.management.dto.LoginRequest;
import com.pharmacy.management.dto.LoginResponse;
import com.pharmacy.management.dto.RefreshTokenRequest;

/**
 * 认证服务接口
 */
public interface AuthService {
    
    /**
     * 用户登录
     */
    LoginResponse login(LoginRequest loginRequest) throws Exception;
    
    /**
     * 刷新Token
     */
    LoginResponse refreshToken(RefreshTokenRequest refreshTokenRequest) throws Exception;
    
    /**
     * 用户登出
     */
    void logout(String token);
    
    /**
     * 获取当前用户信息
     */
    LoginResponse.UserInfo getCurrentUserInfo() throws Exception;
}
