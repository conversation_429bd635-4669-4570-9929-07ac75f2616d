package com.pharmacy.management.service.impl;

import com.pharmacy.management.dto.LoginRequest;
import com.pharmacy.management.dto.LoginResponse;
import com.pharmacy.management.dto.RefreshTokenRequest;
import com.pharmacy.management.entity.Pharmacy;
import com.pharmacy.management.entity.SysUser;
import com.pharmacy.management.entity.UserPharmacy;
import com.pharmacy.management.repository.UserPharmacyRepository;
import com.pharmacy.management.service.AuthService;
import com.pharmacy.management.service.PharmacyService;
import com.pharmacy.management.service.UserService;
import com.pharmacy.management.util.JwtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 认证服务实现
 */
@Service
public class AuthServiceImpl implements AuthService {

    private static final Logger log = LoggerFactory.getLogger(AuthServiceImpl.class);

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private UserPharmacyRepository userPharmacyRepository;

    @Autowired
    private PharmacyService pharmacyService;

    @Value("${jwt.expiration:86400}")
    private Long jwtExpiration;

    @Override
    public LoginResponse login(LoginRequest loginRequest) throws Exception {
        try {
            // 认证用户
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                    loginRequest.getUsername(),
                    loginRequest.getPassword()
                )
            );

            // 获取用户信息
            SysUser user = userService.findByUsername(loginRequest.getUsername());
            if (user == null) {
                throw new Exception("用户不存在");
            }

            if (user.getStatus() != 1) {
                throw new Exception("用户已被禁用");
            }

            // 获取用户角色
            Set<String> roles = userService.getUserRoles(user.getId());
            String role = roles.isEmpty() ? "USER" : roles.iterator().next();

            // 生成Token
            String accessToken = jwtUtil.generateAccessToken(user.getUsername(), user.getId(), role);
            String refreshToken = jwtUtil.generateRefreshToken(user.getUsername(), user.getId());

            // 构建用户信息
            LoginResponse.UserInfo userInfo = buildUserInfo(user, role);

            return new LoginResponse(accessToken, refreshToken, "Bearer", jwtExpiration, userInfo);

        } catch (Exception e) {
            log.error("登录失败: {}", e.getMessage());
            throw new Exception("用户名或密码错误");
        }
    }

    @Override
    public LoginResponse refreshToken(RefreshTokenRequest refreshTokenRequest) throws Exception {
        String refreshToken = refreshTokenRequest.getRefreshToken();
        
        if (!jwtUtil.isRefreshToken(refreshToken)) {
            throw new Exception("无效的刷新Token");
        }

        String username = jwtUtil.getUsernameFromToken(refreshToken);
        if (username == null || jwtUtil.isTokenExpired(refreshToken)) {
            throw new Exception("刷新Token已过期");
        }

        // 获取用户信息
        SysUser user = userService.findByUsername(username);
        if (user == null || user.getStatus() != 1) {
            throw new Exception("用户不存在或已被禁用");
        }

        // 获取用户角色
        Set<String> roles = userService.getUserRoles(user.getId());
        String role = roles.isEmpty() ? "USER" : roles.iterator().next();

        // 生成新的Token
        String newAccessToken = jwtUtil.generateAccessToken(user.getUsername(), user.getId(), role);
        String newRefreshToken = jwtUtil.generateRefreshToken(user.getUsername(), user.getId());

        // 构建用户信息
        LoginResponse.UserInfo userInfo = buildUserInfo(user, role);

        return new LoginResponse(newAccessToken, newRefreshToken, "Bearer", jwtExpiration, userInfo);
    }

    @Override
    public void logout(String token) {
        // 这里可以实现Token黑名单机制
        // 暂时只清除SecurityContext
        SecurityContextHolder.clearContext();
        log.info("用户登出成功");
    }

    @Override
    public LoginResponse.UserInfo getCurrentUserInfo() throws Exception {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new Exception("用户未登录");
        }

        String username = authentication.getName();
        SysUser user = userService.findByUsername(username);
        if (user == null) {
            throw new Exception("用户不存在");
        }

        Set<String> roles = userService.getUserRoles(user.getId());
        String role = roles.isEmpty() ? "USER" : roles.iterator().next();

        return buildUserInfo(user, role);
    }

    /**
     * 构建用户信息
     */
    private LoginResponse.UserInfo buildUserInfo(SysUser user, String role) {
        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
        userInfo.setId(user.getId());
        userInfo.setUsername(user.getUsername());
        userInfo.setRealName(user.getRealName());
        userInfo.setRole(role);

        // 获取用户关联的药店
        List<UserPharmacy> userPharmacies = userPharmacyRepository.findByUserId(user.getId());
        List<LoginResponse.PharmacyInfo> pharmacies = userPharmacies.stream()
            .map(up -> {
                Optional<Pharmacy> pharmacyOpt = pharmacyService.findById(up.getPharmacyId());
                if (pharmacyOpt.isPresent()) {
                    Pharmacy pharmacy = pharmacyOpt.get();
                    return new LoginResponse.PharmacyInfo(
                        pharmacy.getId(),
                        pharmacy.getName(),
                        pharmacy.getAddress()
                    );
                }
                return null;
            })
            .filter(p -> p != null)
            .collect(Collectors.toList());

        userInfo.setPharmacies(pharmacies);

        // 设置当前药店（取第一个，或者从Session中获取）
        if (!pharmacies.isEmpty()) {
            userInfo.setCurrentPharmacyId(pharmacies.get(0).getId());
        }

        return userInfo;
    }
}
