package com.pharmacy.management.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.config.annotation.*;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private PharmacyInterceptor pharmacyInterceptor;

    @Autowired
    private FileUploadConfig fileUploadConfig;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 只对非API路径应用药店拦截器
        registry.addInterceptor(pharmacyInterceptor)
                .excludePathPatterns("/api/**", "/static/**", "/css/**", "/js/**", "/img/**");
    }

    // 移除路径前缀配置，直接在控制器中使用完整路径
    // @Override
    // public void configurePathMatch(PathMatchConfigurer configurer) {
    //     // 为API控制器添加统一前缀
    //     configurer.addPathPrefix("/api/v1", c ->
    //         c.getPackage().getName().contains("com.pharmacy.management.controller.api")
    //     );
    // }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置静态资源路径
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");
        
        registry.addResourceHandler("/css/**")
                .addResourceLocations("classpath:/static/css/");
        
        registry.addResourceHandler("/js/**")
                .addResourceLocations("classpath:/static/js/");
        
        registry.addResourceHandler("/img/**")
                .addResourceLocations("classpath:/static/img/");
        
        registry.addResourceHandler("/plugins/**")
                .addResourceLocations("classpath:/static/plugins/");
        
        registry.addResourceHandler("/dist/**")
                .addResourceLocations("classpath:/static/dist/");
        
        registry.addResourceHandler("/build/**")
                .addResourceLocations("classpath:/static/build/");
                
        registry.addResourceHandler("/vendor/**")
                .addResourceLocations("classpath:/static/vendor/");

        // 配置头像文件访问路径，添加缓存
        registry.addResourceHandler("/uploads/avatars/**")
                .addResourceLocations("file:" + fileUploadConfig.getUploadDir() + "/avatars/")
                .setCachePeriod(86400) // 24小时缓存
                .resourceChain(true);

        // 配置就诊记录图片访问
        registry.addResourceHandler("/medical-records/**")
                .addResourceLocations("file:" + fileUploadConfig.getUploadDir() + "/medical-records/");
    }

    @Override
    public void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
        configurer
            .favorParameter(true)
            .parameterName("mediaType")
            .ignoreAcceptHeader(false)
            .useRegisteredExtensionsOnly(false)
            .defaultContentType(MediaType.APPLICATION_JSON)
            .mediaType("html", MediaType.TEXT_HTML)
            .mediaType("json", MediaType.APPLICATION_JSON)
            .mediaType("css", MediaType.valueOf("text/css"))
            .mediaType("js", MediaType.valueOf("application/javascript"))
            .mediaType("png", MediaType.IMAGE_PNG)
            .mediaType("jpg", MediaType.IMAGE_JPEG)
            .mediaType("jpeg", MediaType.IMAGE_JPEG)
            .mediaType("gif", MediaType.IMAGE_GIF);
    }
} 