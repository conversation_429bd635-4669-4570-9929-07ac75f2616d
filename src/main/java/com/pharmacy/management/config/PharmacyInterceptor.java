package com.pharmacy.management.config;

import com.pharmacy.management.entity.Pharmacy;
import com.pharmacy.management.service.PharmacyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Component
public class PharmacyInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(PharmacyInterceptor.class);

    @Autowired
    private PharmacyService pharmacyService;

    @Autowired
    private PharmacyContext pharmacyContext;

    // 不需要选择药店就能访问的路径
    private static final List<String> WHITELIST = Arrays.asList(
        "/login",
        "/register",
        "/logout",
        "/pharmacies",
        "/pharmacy",
        "/pharmacy/select",
        "/pharmacy/add",
        "/pharmacy/save",
        "/pharmacy/edit",
        "/pharmacy/delete",
        "/css",
        "/js",
        "/img",
        "/plugins",
        "/webjars",
        "/error",
        "/dashboard",
        "/users",
        "/roles",
        "/api"  // API路径
    );

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();
        
        // 检查是否是白名单路径
        for (String path : WHITELIST) {
            if (requestURI.startsWith(path)) {
                return true;
            }
        }

        // 如果未选择药店，重定向到药店选择页面
        if (!pharmacyContext.hasSelectedPharmacy()) {
            response.sendRedirect("/pharmacy/select");
            return false;
        }

        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        if (modelAndView != null) {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated() && !"anonymousUser".equals(authentication.getName())) {
                String username = authentication.getName();
                
                // 获取当前用户可访问的药店列表
                List<Pharmacy> pharmacies = pharmacyService.getPharmaciesForUser(username);
                logger.debug("拦截器: 用户 {} 的药店列表数量: {}", username, pharmacies.size());
                
                // 添加药店列表到模型中
                modelAndView.addObject("pharmacies", pharmacies);
                
                // 添加当前选择的药店到模型中
                Pharmacy currentPharmacy = pharmacyContext.getCurrentPharmacy();
                modelAndView.addObject("currentPharmacy", currentPharmacy);
            }
        }
    }
} 