package com.pharmacy.management.controller.api;

import com.pharmacy.management.dto.ApiResponse;
import com.pharmacy.management.dto.LoginRequest;
import com.pharmacy.management.dto.LoginResponse;
import com.pharmacy.management.dto.RefreshTokenRequest;
import com.pharmacy.management.service.AuthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * API认证控制器
 */
@RestController
@RequestMapping("/api/v1/auth")
public class ApiAuthController {

    private static final Logger log = LoggerFactory.getLogger(ApiAuthController.class);

    @Autowired
    private AuthService authService;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ApiResponse<LoginResponse> login(@Valid @RequestBody LoginRequest loginRequest) {
        try {
            LoginResponse loginResponse = authService.login(loginRequest);
            log.info("用户 {} 登录成功", loginRequest.getUsername());
            return ApiResponse.success("登录成功", loginResponse);
        } catch (Exception e) {
            log.error("用户 {} 登录失败: {}", loginRequest.getUsername(), e.getMessage());
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 刷新Token
     */
    @PostMapping("/refresh")
    public ApiResponse<LoginResponse> refreshToken(@Valid @RequestBody RefreshTokenRequest refreshTokenRequest) {
        try {
            LoginResponse loginResponse = authService.refreshToken(refreshTokenRequest);
            log.info("Token刷新成功");
            return ApiResponse.success("Token刷新成功", loginResponse);
        } catch (Exception e) {
            log.error("Token刷新失败: {}", e.getMessage());
            return ApiResponse.unauthorized(e.getMessage());
        }
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public ApiResponse<Void> logout(HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            authService.logout(token);
            log.info("用户登出成功");
            return ApiResponse.success("登出成功");
        } catch (Exception e) {
            log.error("登出失败: {}", e.getMessage());
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    public ApiResponse<LoginResponse.UserInfo> getCurrentUser() {
        try {
            LoginResponse.UserInfo userInfo = authService.getCurrentUserInfo();
            return ApiResponse.success(userInfo);
        } catch (Exception e) {
            log.error("获取用户信息失败: {}", e.getMessage());
            return ApiResponse.unauthorized(e.getMessage());
        }
    }

    /**
     * 从请求头中提取Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}
