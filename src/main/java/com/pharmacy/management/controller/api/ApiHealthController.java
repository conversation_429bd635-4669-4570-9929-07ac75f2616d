package com.pharmacy.management.controller.api;

import com.pharmacy.management.dto.ApiResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * API健康检查控制器
 */
@RestController
@RequestMapping("/api/v1")
public class ApiHealthController {

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public ApiResponse<Map<String, Object>> health() {
        Map<String, Object> healthInfo = new HashMap<>();
        healthInfo.put("status", "UP");
        healthInfo.put("timestamp", LocalDateTime.now());
        healthInfo.put("service", "pharmacy-management-api");
        healthInfo.put("version", "1.0.0");
        
        return ApiResponse.success("服务运行正常", healthInfo);
    }
}
