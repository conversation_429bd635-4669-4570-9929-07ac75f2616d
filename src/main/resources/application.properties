spring.profiles.active=@spring.profiles.active@

# 服务器配置
server.port=8080

# 数据库配置
spring.datasource.url=*****************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# 数据库连接池配置 - 基本配置在代码中设置
# 不在属性文件中配置连接池参数，避免配置绑定问题
# spring.datasource.hikari.minimum-idle=5
# spring.datasource.hikari.maximum-pool-size=15
# spring.datasource.hikari.idle-timeout=30000
# spring.datasource.hikari.pool-name=PharmacyHikariCP
# spring.datasource.hikari.max-lifetime=1800000
# spring.datasource.hikari.connection-timeout=30000
# spring.datasource.hikari.connection-test-query=SELECT 1

# JPA配置
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.show-sql=true
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true

# Flyway配置
spring.flyway.baseline-on-migrate=true
spring.flyway.enabled=false

# 日期时间格式化
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8

# Thymeleaf配置
spring.thymeleaf.cache=false
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.mode=HTML
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html
spring.thymeleaf.check-template=true
spring.thymeleaf.check-template-location=true
spring.thymeleaf.enabled=true

# 日志配置
logging.level.root=INFO
logging.level.com.pharmacy=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.file.name=logs/pharmacy.log
logging.level.com.pharmacy.management=DEBUG

# 静态资源配置
spring.web.resources.static-locations=classpath:/static/
spring.web.resources.cache.period=0

# 允许循环引用
spring.main.allow-circular-references=true

# 错误处理配置
server.error.include-message=always
server.error.include-binding-errors=always
server.error.include-stacktrace=always
server.error.include-exception=true

# 文件上传配置
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
file.upload.upload-dir=uploads
file.upload.allowed-types=image/jpeg,image/png,image/jpg

# JWT配置
jwt.secret=pharmacy-management-jwt-secret-key-for-token-generation-and-validation-must-be-at-least-512-bits-long
jwt.expiration=86400
jwt.refresh-expiration=604800