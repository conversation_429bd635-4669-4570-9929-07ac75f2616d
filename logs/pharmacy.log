2025-05-29 13:11:12.829  INFO 94282 --- [main] c.p.m.PharmacyManagementApplication      : Starting PharmacyManagementApplication using Java 1.8.0_421 on ZBMAC-061e5e91a with PID 94282 (/Users/<USER>/ai/pharmacy_online/pharmacy/target/classes started by liandahu in /Users/<USER>/ai/pharmacy_online/pharmacy)
2025-05-29 13:11:12.840 DEBUG 94282 --- [main] c.p.m.PharmacyManagementApplication      : Running with Spring Boot v2.5.14, Spring v5.3.20
2025-05-29 13:11:12.857  INFO 94282 --- [main] c.p.m.PharmacyManagementApplication      : The following 1 profile is active: "dev"
2025-05-29 13:11:13.567  INFO 94282 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-29 13:11:13.670  INFO 94282 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 92 ms. Found 8 JPA repository interfaces.
2025-05-29 13:11:14.003  INFO 94282 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@23b8d9f3' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 13:11:14.006  INFO 94282 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 13:11:14.267  INFO 94282 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-05-29 13:11:14.278  INFO 94282 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-29 13:11:14.278  INFO 94282 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-05-29 13:11:14.429  INFO 94282 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-29 13:11:14.430  INFO 94282 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1489 ms
2025-05-29 13:11:14.486 DEBUG 94282 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter      : Filter 'resourceUrlEncodingFilter' configured for use
2025-05-29 13:11:14.524  INFO 94282 --- [main] c.p.management.config.DataSourceConfig   : 已添加maxAllowedPacket参数到JDBC URL: ********************************************************************************************************************************************************************************
2025-05-29 13:11:14.525  INFO 94282 --- [main] c.p.management.config.DataSourceConfig   : 已调整连接池最大生命周期为: 600000ms (10分钟)
2025-05-29 13:11:14.525  INFO 94282 --- [main] c.p.management.config.DataSourceConfig   : 已设置连接超时时间为: 30000ms (30秒)
2025-05-29 13:11:14.525  INFO 94282 --- [main] c.p.management.config.DataSourceConfig   : 已设置空闲超时为: 120000ms (2分钟)
2025-05-29 13:11:14.525  INFO 94282 --- [main] c.p.management.config.DataSourceConfig   : 已设置连接测试查询
2025-05-29 13:11:14.527  INFO 94282 --- [main] c.p.management.config.DataSourceConfig   : 已设置连接保活时间为: 120000ms (2分钟)
2025-05-29 13:11:14.528  INFO 94282 --- [main] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Starting...
2025-05-29 13:11:15.020  INFO 94282 --- [main] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Start completed.
2025-05-29 13:11:15.045  INFO 94282 --- [main] c.p.management.config.DataSourceConfig   : 已初始化数据库连接并设置会话变量
2025-05-29 13:11:15.049  INFO 94282 --- [main] c.p.management.config.DataSourceConfig   : 当前max_allowed_packet值为: 67108864 bytes (64 MB)
2025-05-29 13:11:15.050  WARN 94282 --- [main] c.p.management.config.DataSourceConfig   : 当前max_allowed_packet值(64 MB)小于期望值(256 MB)，请在MySQL配置文件中设置 max_allowed_packet=256M，或使用SET GLOBAL max_allowed_packet=268435456命令设置。
2025-05-29 13:11:15.128  INFO 94282 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-29 13:11:15.177  INFO 94282 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.4.33
2025-05-29 13:11:15.307  INFO 94282 --- [main] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-05-29 13:11:15.398  INFO 94282 --- [main] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-05-29 13:11:17.252  INFO 94282 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-05-29 13:11:17.263  INFO 94282 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-29 13:11:18.043  WARN 94282 --- [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-29 13:11:18.111  INFO 94282 --- [main] c.p.management.config.DataSourceConfig   : 数据库连接验证成功
2025-05-29 13:11:18.782  INFO 94282 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will not secure any request
2025-05-29 13:11:18.962 DEBUG 94282 --- [main] s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-05-29 13:11:19.036 DEBUG 94282 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : 91 mappings in 'requestMappingHandlerMapping'
2025-05-29 13:11:19.300 DEBUG 94282 --- [main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Patterns [/webjars/**, /**, /uploads/**, /static/**, /css/**, /js/**, /img/**, /plugins/**, /dist/**, /build/**, /vendor/**, /uploads/avatars/**, /medical-records/**] in 'resourceHandlerMapping'
2025-05-29 13:11:19.314 DEBUG 94282 --- [main] .m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-05-29 13:11:19.480  INFO 94282 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-05-29 13:11:19.490  INFO 94282 --- [main] c.p.m.PharmacyManagementApplication      : Started PharmacyManagementApplication in 8.403 seconds (JVM running for 8.972)
2025-05-29 13:11:19.492 DEBUG 94282 --- [scheduling-1] c.p.m.config.DatabaseConnectionMonitor   : 数据库连接检查成功
2025-05-29 13:11:19.493  INFO 94282 --- [scheduling-1] c.p.m.config.DatabaseConnectionMonitor   : 已刷新数据库会话变量
2025-05-29 13:11:19.494 DEBUG 94282 --- [scheduling-1] c.p.m.config.DatabaseConnectionMonitor   : 当前max_allowed_packet值为: 67108864 bytes (64 MB)
2025-05-29 13:11:19.500  INFO 94282 --- [main] c.p.m.config.AdminPharmacyInitializer    : 开始初始化系统管理员药店关联...
2025-05-29 13:11:19.722 DEBUG 94282 --- [main] org.hibernate.SQL                        : 
    select
        pharmacy0_.id as id1_3_,
        pharmacy0_.address as address2_3_,
        pharmacy0_.business_hours as business3_3_,
        pharmacy0_.created_at as created_4_3_,
        pharmacy0_.description as descript5_3_,
        pharmacy0_.is_active as is_activ6_3_,
        pharmacy0_.license as license7_3_,
        pharmacy0_.name as name8_3_,
        pharmacy0_.phone as phone9_3_,
        pharmacy0_.updated_at as updated10_3_ 
    from
        pharmacies pharmacy0_
2025-05-29 13:11:19.753  INFO 94282 --- [main] c.p.m.config.AdminPharmacyInitializer    : 为系统管理员 admin 关联 2 个药店
2025-05-29 13:11:19.778 DEBUG 94282 --- [main] org.hibernate.SQL                        : 
    delete 
    from
        user_pharmacy 
    where
        user_id=?
2025-05-29 13:11:19.817 DEBUG 94282 --- [main] org.hibernate.SQL                        : 
    insert 
    into
        user_pharmacy
        (pharmacy_id, user_id) 
    values
        (?, ?)
2025-05-29 13:11:19.825 DEBUG 94282 --- [main] org.hibernate.SQL                        : 
    insert 
    into
        user_pharmacy
        (pharmacy_id, user_id) 
    values
        (?, ?)
2025-05-29 13:11:19.826  INFO 94282 --- [main] c.p.m.config.AdminPharmacyInitializer    : 系统管理员药店关联初始化完成
2025-05-29 13:11:41.686  INFO 94282 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-29 13:11:41.686  INFO 94282 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-29 13:11:41.687 DEBUG 94282 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected StandardServletMultipartResolver
2025-05-29 13:11:41.687 DEBUG 94282 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected AcceptHeaderLocaleResolver
2025-05-29 13:11:41.687 DEBUG 94282 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected FixedThemeResolver
2025-05-29 13:11:41.688 DEBUG 94282 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@45a05350
2025-05-29 13:11:41.688 DEBUG 94282 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected org.springframework.web.servlet.support.SessionFlashMapManager@71e55e48
2025-05-29 13:11:41.688 DEBUG 94282 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-05-29 13:11:41.688  INFO 94282 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-05-29 13:12:19.302  INFO 94282 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-29 13:12:19.305  INFO 94282 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Shutdown initiated...
2025-05-29 13:12:19.311  INFO 94282 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Shutdown completed.
2025-05-29 13:12:33.360  INFO 94633 --- [main] c.p.m.PharmacyManagementApplication      : Starting PharmacyManagementApplication using Java 1.8.0_421 on ZBMAC-061e5e91a with PID 94633 (/Users/<USER>/ai/pharmacy_online/pharmacy/target/classes started by liandahu in /Users/<USER>/ai/pharmacy_online/pharmacy)
2025-05-29 13:12:33.363 DEBUG 94633 --- [main] c.p.m.PharmacyManagementApplication      : Running with Spring Boot v2.5.14, Spring v5.3.20
2025-05-29 13:12:33.363  INFO 94633 --- [main] c.p.m.PharmacyManagementApplication      : The following 1 profile is active: "dev"
2025-05-29 13:12:33.832  INFO 94633 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-29 13:12:33.898  INFO 94633 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 61 ms. Found 8 JPA repository interfaces.
2025-05-29 13:12:34.266  INFO 94633 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@767191b1' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 13:12:34.270  INFO 94633 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 13:12:34.519  INFO 94633 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-05-29 13:12:34.527  INFO 94633 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-29 13:12:34.527  INFO 94633 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-05-29 13:12:34.632  INFO 94633 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-29 13:12:34.632  INFO 94633 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1207 ms
2025-05-29 13:12:34.671 DEBUG 94633 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter      : Filter 'resourceUrlEncodingFilter' configured for use
2025-05-29 13:12:34.702  INFO 94633 --- [main] c.p.management.config.DataSourceConfig   : 已添加maxAllowedPacket参数到JDBC URL: ********************************************************************************************************************************************************************************
2025-05-29 13:12:34.703  INFO 94633 --- [main] c.p.management.config.DataSourceConfig   : 已调整连接池最大生命周期为: 600000ms (10分钟)
2025-05-29 13:12:34.703  INFO 94633 --- [main] c.p.management.config.DataSourceConfig   : 已设置连接超时时间为: 30000ms (30秒)
2025-05-29 13:12:34.703  INFO 94633 --- [main] c.p.management.config.DataSourceConfig   : 已设置空闲超时为: 120000ms (2分钟)
2025-05-29 13:12:34.703  INFO 94633 --- [main] c.p.management.config.DataSourceConfig   : 已设置连接测试查询
2025-05-29 13:12:34.704  INFO 94633 --- [main] c.p.management.config.DataSourceConfig   : 已设置连接保活时间为: 120000ms (2分钟)
2025-05-29 13:12:34.705  INFO 94633 --- [main] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Starting...
2025-05-29 13:12:34.883  INFO 94633 --- [main] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Start completed.
2025-05-29 13:12:34.886  INFO 94633 --- [main] c.p.management.config.DataSourceConfig   : 已初始化数据库连接并设置会话变量
2025-05-29 13:12:34.889  INFO 94633 --- [main] c.p.management.config.DataSourceConfig   : 当前max_allowed_packet值为: 67108864 bytes (64 MB)
2025-05-29 13:12:34.889  WARN 94633 --- [main] c.p.management.config.DataSourceConfig   : 当前max_allowed_packet值(64 MB)小于期望值(256 MB)，请在MySQL配置文件中设置 max_allowed_packet=256M，或使用SET GLOBAL max_allowed_packet=268435456命令设置。
2025-05-29 13:12:34.953  INFO 94633 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-29 13:12:34.990  INFO 94633 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.4.33
2025-05-29 13:12:35.083  INFO 94633 --- [main] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-05-29 13:12:35.156  INFO 94633 --- [main] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-05-29 13:12:35.755  INFO 94633 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-05-29 13:12:35.764  INFO 94633 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-29 13:12:36.577  WARN 94633 --- [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-29 13:12:36.641  INFO 94633 --- [main] c.p.management.config.DataSourceConfig   : 数据库连接验证成功
2025-05-29 13:12:37.017  INFO 94633 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will not secure any request
2025-05-29 13:12:37.140 DEBUG 94633 --- [main] s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-05-29 13:12:37.199 DEBUG 94633 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : 91 mappings in 'requestMappingHandlerMapping'
2025-05-29 13:12:37.437 DEBUG 94633 --- [main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Patterns [/webjars/**, /**, /uploads/**, /static/**, /css/**, /js/**, /img/**, /plugins/**, /dist/**, /build/**, /vendor/**, /uploads/avatars/**, /medical-records/**] in 'resourceHandlerMapping'
2025-05-29 13:12:37.452 DEBUG 94633 --- [main] .m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-05-29 13:12:37.596  INFO 94633 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-05-29 13:12:37.606  INFO 94633 --- [main] c.p.m.PharmacyManagementApplication      : Started PharmacyManagementApplication in 4.64 seconds (JVM running for 4.946)
2025-05-29 13:12:37.607 DEBUG 94633 --- [scheduling-1] c.p.m.config.DatabaseConnectionMonitor   : 数据库连接检查成功
2025-05-29 13:12:37.608  INFO 94633 --- [scheduling-1] c.p.m.config.DatabaseConnectionMonitor   : 已刷新数据库会话变量
2025-05-29 13:12:37.609 DEBUG 94633 --- [scheduling-1] c.p.m.config.DatabaseConnectionMonitor   : 当前max_allowed_packet值为: 67108864 bytes (64 MB)
2025-05-29 13:12:37.616  INFO 94633 --- [main] c.p.m.config.AdminPharmacyInitializer    : 开始初始化系统管理员药店关联...
2025-05-29 13:12:37.718 DEBUG 94633 --- [main] org.hibernate.SQL                        : 
    select
        pharmacy0_.id as id1_3_,
        pharmacy0_.address as address2_3_,
        pharmacy0_.business_hours as business3_3_,
        pharmacy0_.created_at as created_4_3_,
        pharmacy0_.description as descript5_3_,
        pharmacy0_.is_active as is_activ6_3_,
        pharmacy0_.license as license7_3_,
        pharmacy0_.name as name8_3_,
        pharmacy0_.phone as phone9_3_,
        pharmacy0_.updated_at as updated10_3_ 
    from
        pharmacies pharmacy0_
2025-05-29 13:12:37.750  INFO 94633 --- [main] c.p.m.config.AdminPharmacyInitializer    : 为系统管理员 admin 关联 2 个药店
2025-05-29 13:12:37.777 DEBUG 94633 --- [main] org.hibernate.SQL                        : 
    delete 
    from
        user_pharmacy 
    where
        user_id=?
2025-05-29 13:12:37.803 DEBUG 94633 --- [main] org.hibernate.SQL                        : 
    insert 
    into
        user_pharmacy
        (pharmacy_id, user_id) 
    values
        (?, ?)
2025-05-29 13:12:37.811 DEBUG 94633 --- [main] org.hibernate.SQL                        : 
    insert 
    into
        user_pharmacy
        (pharmacy_id, user_id) 
    values
        (?, ?)
2025-05-29 13:12:37.813  INFO 94633 --- [main] c.p.m.config.AdminPharmacyInitializer    : 系统管理员药店关联初始化完成
2025-05-29 13:13:00.155  INFO 94633 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-29 13:13:00.155  INFO 94633 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-29 13:13:00.156 DEBUG 94633 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected StandardServletMultipartResolver
2025-05-29 13:13:00.156 DEBUG 94633 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected AcceptHeaderLocaleResolver
2025-05-29 13:13:00.156 DEBUG 94633 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected FixedThemeResolver
2025-05-29 13:13:00.159 DEBUG 94633 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@7a482b3a
2025-05-29 13:13:00.160 DEBUG 94633 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected org.springframework.web.servlet.support.SessionFlashMapManager@4d7fba20
2025-05-29 13:13:00.160 DEBUG 94633 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-05-29 13:13:00.160  INFO 94633 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
2025-05-29 13:13:00.178 DEBUG 94633 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : GET "/api/v1/test/health", parameters={}
2025-05-29 13:13:00.182 DEBUG 94633 --- [http-nio-8080-exec-1] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.pharmacy.management.api.TestController#health()
2025-05-29 13:13:00.204 DEBUG 94633 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed 302 FOUND
2025-05-29 13:13:32.780 DEBUG 94633 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : GET "/api/v1/test/health", parameters={}
2025-05-29 13:13:32.780 DEBUG 94633 --- [http-nio-8080-exec-2] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.pharmacy.management.api.TestController#health()
2025-05-29 13:13:32.781 DEBUG 94633 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : Completed 302 FOUND
2025-05-29 13:13:38.259  INFO 94633 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-29 13:13:38.260  INFO 94633 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Shutdown initiated...
2025-05-29 13:13:38.264  INFO 94633 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Shutdown completed.
2025-05-29 13:13:54.023  INFO 94834 --- [main] c.p.m.PharmacyManagementApplication      : Starting PharmacyManagementApplication using Java 1.8.0_421 on ZBMAC-061e5e91a with PID 94834 (/Users/<USER>/ai/pharmacy_online/pharmacy/target/classes started by liandahu in /Users/<USER>/ai/pharmacy_online/pharmacy)
2025-05-29 13:13:54.027 DEBUG 94834 --- [main] c.p.m.PharmacyManagementApplication      : Running with Spring Boot v2.5.14, Spring v5.3.20
2025-05-29 13:13:54.028  INFO 94834 --- [main] c.p.m.PharmacyManagementApplication      : The following 1 profile is active: "dev"
2025-05-29 13:13:54.496  INFO 94834 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-29 13:13:54.565  INFO 94834 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 64 ms. Found 8 JPA repository interfaces.
2025-05-29 13:13:54.820  INFO 94834 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@5e1d03d7' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 13:13:54.822  INFO 94834 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 13:13:55.010  INFO 94834 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-05-29 13:13:55.017  INFO 94834 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-29 13:13:55.018  INFO 94834 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-05-29 13:13:55.137  INFO 94834 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-29 13:13:55.138  INFO 94834 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1043 ms
2025-05-29 13:13:55.183 DEBUG 94834 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter      : Filter 'resourceUrlEncodingFilter' configured for use
2025-05-29 13:13:55.212  INFO 94834 --- [main] c.p.management.config.DataSourceConfig   : 已添加maxAllowedPacket参数到JDBC URL: ********************************************************************************************************************************************************************************
2025-05-29 13:13:55.212  INFO 94834 --- [main] c.p.management.config.DataSourceConfig   : 已调整连接池最大生命周期为: 600000ms (10分钟)
2025-05-29 13:13:55.212  INFO 94834 --- [main] c.p.management.config.DataSourceConfig   : 已设置连接超时时间为: 30000ms (30秒)
2025-05-29 13:13:55.213  INFO 94834 --- [main] c.p.management.config.DataSourceConfig   : 已设置空闲超时为: 120000ms (2分钟)
2025-05-29 13:13:55.213  INFO 94834 --- [main] c.p.management.config.DataSourceConfig   : 已设置连接测试查询
2025-05-29 13:13:55.213  INFO 94834 --- [main] c.p.management.config.DataSourceConfig   : 已设置连接保活时间为: 120000ms (2分钟)
2025-05-29 13:13:55.214  INFO 94834 --- [main] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Starting...
2025-05-29 13:13:55.388  INFO 94834 --- [main] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Start completed.
2025-05-29 13:13:55.391  INFO 94834 --- [main] c.p.management.config.DataSourceConfig   : 已初始化数据库连接并设置会话变量
2025-05-29 13:13:55.394  INFO 94834 --- [main] c.p.management.config.DataSourceConfig   : 当前max_allowed_packet值为: 67108864 bytes (64 MB)
2025-05-29 13:13:55.394  WARN 94834 --- [main] c.p.management.config.DataSourceConfig   : 当前max_allowed_packet值(64 MB)小于期望值(256 MB)，请在MySQL配置文件中设置 max_allowed_packet=256M，或使用SET GLOBAL max_allowed_packet=268435456命令设置。
2025-05-29 13:13:55.461  INFO 94834 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-29 13:13:55.493  INFO 94834 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.4.33
2025-05-29 13:13:55.580  INFO 94834 --- [main] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-05-29 13:13:55.649  INFO 94834 --- [main] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-05-29 13:13:56.232  INFO 94834 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-05-29 13:13:56.243  INFO 94834 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-29 13:13:56.996  WARN 94834 --- [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-29 13:13:57.060  INFO 94834 --- [main] c.p.management.config.DataSourceConfig   : 数据库连接验证成功
2025-05-29 13:13:57.431  INFO 94834 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will not secure any request
2025-05-29 13:13:57.553 DEBUG 94834 --- [main] s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-05-29 13:13:57.614 DEBUG 94834 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : 91 mappings in 'requestMappingHandlerMapping'
2025-05-29 13:13:57.880 DEBUG 94834 --- [main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Patterns [/webjars/**, /**, /uploads/**, /static/**, /css/**, /js/**, /img/**, /plugins/**, /dist/**, /build/**, /vendor/**, /uploads/avatars/**, /medical-records/**] in 'resourceHandlerMapping'
2025-05-29 13:13:57.896 DEBUG 94834 --- [main] .m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-05-29 13:13:58.049  INFO 94834 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-05-29 13:13:58.057  INFO 94834 --- [main] c.p.m.PharmacyManagementApplication      : Started PharmacyManagementApplication in 4.414 seconds (JVM running for 4.687)
2025-05-29 13:13:58.059 DEBUG 94834 --- [scheduling-1] c.p.m.config.DatabaseConnectionMonitor   : 数据库连接检查成功
2025-05-29 13:13:58.060  INFO 94834 --- [scheduling-1] c.p.m.config.DatabaseConnectionMonitor   : 已刷新数据库会话变量
2025-05-29 13:13:58.060 DEBUG 94834 --- [scheduling-1] c.p.m.config.DatabaseConnectionMonitor   : 当前max_allowed_packet值为: 67108864 bytes (64 MB)
2025-05-29 13:13:58.067  INFO 94834 --- [main] c.p.m.config.AdminPharmacyInitializer    : 开始初始化系统管理员药店关联...
2025-05-29 13:13:58.148 DEBUG 94834 --- [main] org.hibernate.SQL                        : 
    select
        pharmacy0_.id as id1_3_,
        pharmacy0_.address as address2_3_,
        pharmacy0_.business_hours as business3_3_,
        pharmacy0_.created_at as created_4_3_,
        pharmacy0_.description as descript5_3_,
        pharmacy0_.is_active as is_activ6_3_,
        pharmacy0_.license as license7_3_,
        pharmacy0_.name as name8_3_,
        pharmacy0_.phone as phone9_3_,
        pharmacy0_.updated_at as updated10_3_ 
    from
        pharmacies pharmacy0_
2025-05-29 13:13:58.167  INFO 94834 --- [main] c.p.m.config.AdminPharmacyInitializer    : 为系统管理员 admin 关联 2 个药店
2025-05-29 13:13:58.189 DEBUG 94834 --- [main] org.hibernate.SQL                        : 
    delete 
    from
        user_pharmacy 
    where
        user_id=?
2025-05-29 13:13:58.212 DEBUG 94834 --- [main] org.hibernate.SQL                        : 
    insert 
    into
        user_pharmacy
        (pharmacy_id, user_id) 
    values
        (?, ?)
2025-05-29 13:13:58.218 DEBUG 94834 --- [main] org.hibernate.SQL                        : 
    insert 
    into
        user_pharmacy
        (pharmacy_id, user_id) 
    values
        (?, ?)
2025-05-29 13:13:58.220  INFO 94834 --- [main] c.p.m.config.AdminPharmacyInitializer    : 系统管理员药店关联初始化完成
2025-05-29 13:17:38.298  INFO 94834 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-29 13:17:38.300  INFO 94834 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-29 13:17:38.301 DEBUG 94834 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected StandardServletMultipartResolver
2025-05-29 13:17:38.301 DEBUG 94834 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected AcceptHeaderLocaleResolver
2025-05-29 13:17:38.301 DEBUG 94834 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected FixedThemeResolver
2025-05-29 13:17:38.311 DEBUG 94834 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@58b30e3e
2025-05-29 13:17:38.312 DEBUG 94834 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected org.springframework.web.servlet.support.SessionFlashMapManager@6c167296
2025-05-29 13:17:38.312 DEBUG 94834 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-05-29 13:17:38.312  INFO 94834 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 12 ms
2025-05-29 13:17:38.346 DEBUG 94834 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : GET "/api/v1/test/health", parameters={}
2025-05-29 13:17:38.351 DEBUG 94834 --- [http-nio-8080-exec-1] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.pharmacy.management.api.TestController#health()
2025-05-29 13:17:38.386 DEBUG 94834 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed 302 FOUND
2025-05-29 13:17:56.142 DEBUG 94834 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : GET "/api/v1/test/health", parameters={}
2025-05-29 13:17:56.144 DEBUG 94834 --- [http-nio-8080-exec-2] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.pharmacy.management.api.TestController#health()
2025-05-29 13:17:56.147 DEBUG 94834 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : Completed 302 FOUND
2025-05-29 13:18:01.791  INFO 94834 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-29 13:18:01.795  INFO 94834 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Shutdown initiated...
2025-05-29 13:18:01.801  INFO 94834 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Shutdown completed.
2025-05-29 13:18:22.116  INFO 95296 --- [main] c.p.m.PharmacyManagementApplication      : Starting PharmacyManagementApplication using Java 1.8.0_421 on ZBMAC-061e5e91a with PID 95296 (/Users/<USER>/ai/pharmacy_online/pharmacy/target/classes started by liandahu in /Users/<USER>/ai/pharmacy_online/pharmacy)
2025-05-29 13:18:22.121 DEBUG 95296 --- [main] c.p.m.PharmacyManagementApplication      : Running with Spring Boot v2.5.14, Spring v5.3.20
2025-05-29 13:18:22.123  INFO 95296 --- [main] c.p.m.PharmacyManagementApplication      : The following 1 profile is active: "dev"
2025-05-29 13:18:22.891  INFO 95296 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-29 13:18:23.022  INFO 95296 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 120 ms. Found 8 JPA repository interfaces.
2025-05-29 13:18:23.476  INFO 95296 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@48528634' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 13:18:23.479  INFO 95296 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 13:18:23.769  INFO 95296 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-05-29 13:18:23.779  INFO 95296 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-29 13:18:23.780  INFO 95296 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-05-29 13:18:24.037  INFO 95296 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-29 13:18:24.037  INFO 95296 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1840 ms
2025-05-29 13:18:24.093 DEBUG 95296 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter      : Filter 'resourceUrlEncodingFilter' configured for use
2025-05-29 13:18:24.129  INFO 95296 --- [main] c.p.management.config.DataSourceConfig   : 已添加maxAllowedPacket参数到JDBC URL: ********************************************************************************************************************************************************************************
2025-05-29 13:18:24.130  INFO 95296 --- [main] c.p.management.config.DataSourceConfig   : 已调整连接池最大生命周期为: 600000ms (10分钟)
2025-05-29 13:18:24.131  INFO 95296 --- [main] c.p.management.config.DataSourceConfig   : 已设置连接超时时间为: 30000ms (30秒)
2025-05-29 13:18:24.131  INFO 95296 --- [main] c.p.management.config.DataSourceConfig   : 已设置空闲超时为: 120000ms (2分钟)
2025-05-29 13:18:24.131  INFO 95296 --- [main] c.p.management.config.DataSourceConfig   : 已设置连接测试查询
2025-05-29 13:18:24.132  INFO 95296 --- [main] c.p.management.config.DataSourceConfig   : 已设置连接保活时间为: 120000ms (2分钟)
2025-05-29 13:18:24.133  INFO 95296 --- [main] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Starting...
2025-05-29 13:18:24.351  INFO 95296 --- [main] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Start completed.
2025-05-29 13:18:24.356  INFO 95296 --- [main] c.p.management.config.DataSourceConfig   : 已初始化数据库连接并设置会话变量
2025-05-29 13:18:24.359  INFO 95296 --- [main] c.p.management.config.DataSourceConfig   : 当前max_allowed_packet值为: 67108864 bytes (64 MB)
2025-05-29 13:18:24.359  WARN 95296 --- [main] c.p.management.config.DataSourceConfig   : 当前max_allowed_packet值(64 MB)小于期望值(256 MB)，请在MySQL配置文件中设置 max_allowed_packet=256M，或使用SET GLOBAL max_allowed_packet=268435456命令设置。
2025-05-29 13:18:24.436  INFO 95296 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-29 13:18:24.474  INFO 95296 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.4.33
2025-05-29 13:18:24.585  INFO 95296 --- [main] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-05-29 13:18:24.681  INFO 95296 --- [main] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-05-29 13:18:25.641  INFO 95296 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-05-29 13:18:25.653  INFO 95296 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-29 13:18:26.730  WARN 95296 --- [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-29 13:18:26.803  INFO 95296 --- [main] c.p.management.config.DataSourceConfig   : 数据库连接验证成功
2025-05-29 13:18:27.384  INFO 95296 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will not secure any request
2025-05-29 13:18:27.573 DEBUG 95296 --- [main] s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-05-29 13:18:27.649 DEBUG 95296 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : 91 mappings in 'requestMappingHandlerMapping'
2025-05-29 13:18:28.017 DEBUG 95296 --- [main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Patterns [/webjars/**, /**, /uploads/**, /static/**, /css/**, /js/**, /img/**, /plugins/**, /dist/**, /build/**, /vendor/**, /uploads/avatars/**, /medical-records/**] in 'resourceHandlerMapping'
2025-05-29 13:18:28.039 DEBUG 95296 --- [main] .m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-05-29 13:18:28.249  INFO 95296 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-05-29 13:18:28.262  INFO 95296 --- [main] c.p.m.PharmacyManagementApplication      : Started PharmacyManagementApplication in 6.649 seconds (JVM running for 7.054)
2025-05-29 13:18:28.264 DEBUG 95296 --- [scheduling-1] c.p.m.config.DatabaseConnectionMonitor   : 数据库连接检查成功
2025-05-29 13:18:28.265  INFO 95296 --- [scheduling-1] c.p.m.config.DatabaseConnectionMonitor   : 已刷新数据库会话变量
2025-05-29 13:18:28.266 DEBUG 95296 --- [scheduling-1] c.p.m.config.DatabaseConnectionMonitor   : 当前max_allowed_packet值为: 67108864 bytes (64 MB)
2025-05-29 13:18:28.278  INFO 95296 --- [main] c.p.m.config.AdminPharmacyInitializer    : 开始初始化系统管理员药店关联...
2025-05-29 13:18:28.399 DEBUG 95296 --- [main] org.hibernate.SQL                        : 
    select
        pharmacy0_.id as id1_3_,
        pharmacy0_.address as address2_3_,
        pharmacy0_.business_hours as business3_3_,
        pharmacy0_.created_at as created_4_3_,
        pharmacy0_.description as descript5_3_,
        pharmacy0_.is_active as is_activ6_3_,
        pharmacy0_.license as license7_3_,
        pharmacy0_.name as name8_3_,
        pharmacy0_.phone as phone9_3_,
        pharmacy0_.updated_at as updated10_3_ 
    from
        pharmacies pharmacy0_
2025-05-29 13:18:28.423  INFO 95296 --- [main] c.p.m.config.AdminPharmacyInitializer    : 为系统管理员 admin 关联 2 个药店
2025-05-29 13:18:28.449 DEBUG 95296 --- [main] org.hibernate.SQL                        : 
    delete 
    from
        user_pharmacy 
    where
        user_id=?
2025-05-29 13:18:28.475 DEBUG 95296 --- [main] org.hibernate.SQL                        : 
    insert 
    into
        user_pharmacy
        (pharmacy_id, user_id) 
    values
        (?, ?)
2025-05-29 13:18:28.482 DEBUG 95296 --- [main] org.hibernate.SQL                        : 
    insert 
    into
        user_pharmacy
        (pharmacy_id, user_id) 
    values
        (?, ?)
2025-05-29 13:18:28.485  INFO 95296 --- [main] c.p.m.config.AdminPharmacyInitializer    : 系统管理员药店关联初始化完成
2025-05-29 13:18:39.999  INFO 95296 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-29 13:18:39.999  INFO 95296 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-29 13:18:40.000 DEBUG 95296 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected StandardServletMultipartResolver
2025-05-29 13:18:40.000 DEBUG 95296 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected AcceptHeaderLocaleResolver
2025-05-29 13:18:40.000 DEBUG 95296 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected FixedThemeResolver
2025-05-29 13:18:40.001 DEBUG 95296 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@3da6950f
2025-05-29 13:18:40.002 DEBUG 95296 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected org.springframework.web.servlet.support.SessionFlashMapManager@6e4bccc
2025-05-29 13:18:40.002 DEBUG 95296 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-05-29 13:18:40.003  INFO 95296 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
2025-05-29 13:18:40.024 DEBUG 95296 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : GET "/api/v1/test/health", parameters={}
2025-05-29 13:18:40.027 DEBUG 95296 --- [http-nio-8080-exec-1] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.pharmacy.management.api.TestController#health()
2025-05-29 13:18:40.081 DEBUG 95296 --- [http-nio-8080-exec-1] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-29 13:18:40.081 DEBUG 95296 --- [http-nio-8080-exec-1] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=200, message=健康检查通过, data={message=API服务正常运行, status=UP, timestamp=2025-05-29T13:18 (truncated)...]
2025-05-29 13:18:40.103 DEBUG 95296 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-29 13:18:45.455 DEBUG 95296 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : GET "/api/v1/test/error", parameters={}
2025-05-29 13:18:45.456 DEBUG 95296 --- [http-nio-8080-exec-2] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.pharmacy.management.api.TestController#testError()
2025-05-29 13:18:45.458 DEBUG 95296 --- [http-nio-8080-exec-2] .m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.pharmacy.management.config.ApiExceptionHandler#handleRuntimeException(HttpServletRequest, RuntimeException)
2025-05-29 13:18:45.465 ERROR 95296 --- [http-nio-8080-exec-2] c.p.m.config.ApiExceptionHandler         : API请求: /api/v1/test/error 运行时异常

java.lang.RuntimeException: 这是一个测试异常
	at com.pharmacy.management.api.TestController.testError(TestController.java:38) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_421]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_421]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_421]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_421]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [spring-webmvc-5.3.20.jar:5.3.20]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655) [tomcat-embed-core-9.0.63.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.20.jar:5.3.20]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764) [tomcat-embed-core-9.0.63.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) [tomcat-embed-websocket-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:218) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:212) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183) [spring-security-web-5.5.8.jar:5.5.8]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) [spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) [spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at java.lang.Thread.run(Thread.java:750) [na:1.8.0_421]

2025-05-29 13:18:45.469 DEBUG 95296 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.HttpEntityMethodProcessor  : Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-29 13:18:45.470 DEBUG 95296 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.HttpEntityMethodProcessor  : Writing [ApiResponse(code=500, message=操作失败: 这是一个测试异常, data=null, timestamp=1748495925466, path=/api/v1/test/ (truncated)...]
2025-05-29 13:18:45.471 DEBUG 95296 --- [http-nio-8080-exec-2] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [java.lang.RuntimeException: 这是一个测试异常]
2025-05-29 13:18:45.471 DEBUG 95296 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : Completed 500 INTERNAL_SERVER_ERROR
2025-05-29 13:18:51.050 DEBUG 95296 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet        : GET "/api/v1/test/health", parameters={}
2025-05-29 13:18:51.051 DEBUG 95296 --- [http-nio-8080-exec-3] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.pharmacy.management.api.TestController#health()
2025-05-29 13:18:51.052 DEBUG 95296 --- [http-nio-8080-exec-3] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-29 13:18:51.053 DEBUG 95296 --- [http-nio-8080-exec-3] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=200, message=健康检查通过, data={message=API服务正常运行, status=UP, timestamp=2025-05-29T13:18 (truncated)...]
2025-05-29 13:18:51.055 DEBUG 95296 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-29 13:36:30.335  WARN 95296 --- [PharmacyHikariCP housekeeper] com.zaxxer.hikari.pool.HikariPool        : PharmacyHikariCP - Thread starvation or clock leap detected (housekeeper delta=15m53s538ms).
2025-05-29 13:37:23.936  WARN 95296 --- [PharmacyHikariCP housekeeper] com.zaxxer.hikari.pool.HikariPool        : PharmacyHikariCP - Thread starvation or clock leap detected (housekeeper delta=53s611ms).
2025-05-29 13:38:41.132  WARN 95296 --- [PharmacyHikariCP housekeeper] com.zaxxer.hikari.pool.HikariPool        : PharmacyHikariCP - Thread starvation or clock leap detected (housekeeper delta=1m17s196ms).
2025-05-29 13:48:08.345  WARN 95296 --- [PharmacyHikariCP housekeeper] com.zaxxer.hikari.pool.HikariPool        : PharmacyHikariCP - Thread starvation or clock leap detected (housekeeper delta=9m27s213ms).
2025-05-29 13:48:34.387  INFO 95296 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-29 13:48:34.408  INFO 95296 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Shutdown initiated...
2025-05-29 13:48:34.424  INFO 95296 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Shutdown completed.
